import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';
import 'package:koc_app/src/modules/shared/widget/no_data.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';

mixin ReportMixin {
  Widget buildAddFilterMessage(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 12.r,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SvgPicture.asset(
            'assets/images/search.svg',
            width: 100.r,
          ),
          Text(
            'Add filters to determine what data is shown in your report.',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(
                  color: const Color(0xFF767676),
                ),
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }

  Widget buildNoResultBody(BuildContext context, VoidCallback onTap) {
    return Container(
      padding: EdgeInsets.all(16.r),
      alignment: Alignment.center,
      height: 600.r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset('assets/images/mascot.svg'),
          Text('No results found', style: Theme.of(context).textTheme.bodyLarge),
          Text('Please try another search',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676))),
          SizedBox(
            height: 10.r,
          ),
          CommonElevatedButton('Clear filters', onTap),
        ],
      ),
    );
  }

  DataCell buildDataCell(BuildContext context, String text,
      {Color color = const Color(0xFF464646), VoidCallback? onTap, Alignment? alignment}) {
    return DataCell(
      onTap: onTap,
      Container(
        alignment: alignment,
        child: Text(
          text,
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: color),
        ),
      ),
    );
  }

  DataColumn buildDataColumn(BuildContext context, String text) {
    return DataColumn(
        label: Text(text, style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold)));
  }

  Widget buildTotalRewardCard(BuildContext context, String currency, double totalReward) {
    return Container(
        width: double.infinity,
        padding: EdgeInsets.all(8.r),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
        child: Column(
          spacing: 4.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${currency.currencySymbol}${totalReward.toCommaSeparated()}',
              style: context.textBodySmall(fontWeight: FontWeight.w500),
            ),
            Text(
              'Affiliate marketing program total reward',
              style: context.textLabelLarge(
                fontWeight: FontWeight.w500,
                color: const Color(0xFF767676),
              ),
            ),
          ],
        ));
  }

  Widget buildCampaignRewardTable(BuildContext context, String currency, List<CampaignReward> campaignRewards) {
    if (campaignRewards.isEmpty) {
      return const NoDataReportWidget();
    }
    return _buildStickyHeaderTable(
      context: context,
      currency: currency,
      campaignRewards: campaignRewards,
    );
  }

  Widget _buildStickyHeaderTable({
    required BuildContext context,
    required String currency,
    required List<CampaignReward> campaignRewards,
  }) {
    final double campaignColumnWidth = 200.r;
    final double monthColumnWidth = 120.r;
    final double rewardColumnWidth = 150.r;

    return _StickyHeaderTableWidget(
      headerBuilder: (scrollController) => Container(
        color: const Color(0xFFF2F2F2),
        child: SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          physics: const ClampingScrollPhysics(),
          child: Row(
            children: [
              _buildHeaderCell(context, 'Campaign', campaignColumnWidth),
              _buildHeaderCell(context, 'Month', monthColumnWidth),
              _buildHeaderCell(context, 'Reward (${currency.currencySymbol})', rewardColumnWidth,
                  alignment: Alignment.centerRight),
            ],
          ),
        ),
      ),
      bodyBuilder: (scrollController) => SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          physics: const ClampingScrollPhysics(),
          child: Column(
            children: campaignRewards.map((e) {
              return Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
                  ),
                ),
                child: Row(
                  children: [
                    _buildBodyCell(context, e.campaignName, campaignColumnWidth),
                    _buildBodyCell(
                      context,
                      DateFormat(yearMonthFormat).parse(e.rewardMonth).toMonthAndYear(),
                      monthColumnWidth,
                    ),
                    _buildBodyCell(
                      context,
                      e.reward.toCommaSeparated(),
                      rewardColumnWidth,
                      alignment: Alignment.centerRight,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text, double width, {Alignment? alignment}) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 20.r), // Optimized for touch
      alignment: alignment,
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildBodyCell(BuildContext context, String text, double width, {Alignment? alignment}) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 20.r),
      alignment: alignment,
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF464646)),
      ),
    );
  }

  /// Calculate dynamic interval for Y-axis based on max value
  /// Always creates exactly 5 intervals (6 grid lines including 0)
  /// Examples:
  /// - For 1M: returns 200k (0, 200k, 400k, 600k, 800k, 1M)
  /// - For 1000: returns 200 (0, 200, 400, 600, 800, 1000)
  /// - For 3000: returns 600 (0, 600, 1200, 1800, 2400, 3000)
  double calculateDynamicInterval(int maxValue) {
    if (maxValue <= 0) return 100;

    /// Always aim for exactly 5 intervals (6 grid lines)
    double roughInterval = maxValue / 5.0;

    int magnitude = 1;
    while (magnitude * 10 <= roughInterval) {
      magnitude *= 10;
    }

    /// Choose the smallest nice round number that accommodates the data
    /// This ensures we always get exactly 5 intervals
    double interval;
    if (roughInterval <= magnitude * 1) {
      interval = magnitude * 1.0;
    } else if (roughInterval <= magnitude * 2) {
      interval = magnitude * 2.0;
    } else if (roughInterval <= magnitude * 5) {
      interval = magnitude * 5.0;
    } else {
      interval = magnitude * 10.0;
    }

    return interval;
  }

  /// Calculate dynamic interval for decimal values (like CVR percentages)
  /// Note: CVR values from API are already divided by 100 (0.03 = 3%, 0.05 = 5%)
  /// Always creates exactly 5 intervals (6 grid lines including 0)
  ///
  /// Examples:
  /// - For 0.03 (3%): returns 0.006 (0, 0.6, 1.2, 1.8, 2.4, 3.0) → displays as (0, 0.6, 1.2, 1.8, 2.4, 3)
  /// - For 0.08 (8%): returns 0.016 (0, 1.6, 3.2, 4.8, 6.4, 8.0) → displays as (0, 1.6, 3.2, 4.8, 6.4, 8)
  /// - For 0.15 (15%): returns 0.03 (0, 3, 6, 9, 12, 15) → displays as (0, 3, 6, 9, 12, 15)
  /// - For 0.35 (35%): returns 0.07 (0, 7, 14, 21, 28, 35) → displays as (0, 7, 14, 21, 28, 35)
  double calculateDynamicIntervalForDecimals(double maxValue) {
    if (maxValue <= 0) return 0.002; // Default interval

    double roughInterval = maxValue / 5.0;

    double roughPercentage = roughInterval * 100;

    double magnitude = 0.1;
    while (magnitude * 10 <= roughPercentage) {
      magnitude *= 10;
    }

    double intervalPercentage;
    if (roughPercentage <= magnitude * 1) {
      intervalPercentage = magnitude * 1;
    } else if (roughPercentage <= magnitude * 2) {
      intervalPercentage = magnitude * 2;
    } else if (roughPercentage <= magnitude * 5) {
      intervalPercentage = magnitude * 5;
    } else {
      intervalPercentage = magnitude * 10;
    }

    return intervalPercentage / 100;
  }

  /// Format number for chart display
  /// < 1,000: Use plain number (0, 100, 999)
  /// >= 1,000: Use compact format (1k, 2k, 10k, 1M)
  /// For CVR: converts 0.03 to "3" (representing 3%)
  String formatChartNumber(num value) {
    if (value is double && value < 1.0 && value > 0) {
      final percentage = value * 100;
      if (percentage % 1 == 0) {
        return percentage.toInt().toString();
      } else {
        return percentage.toStringAsFixed(1);
      }
    } else if (value == 0) {
      return '0';
    }

    final intValue = value.toInt();
    return intValue >= 1000 ? intValue.toCompactFormat() : intValue.toString();
  }

  /// Format number for tooltip display with 2 decimal places
  /// Note: CVR values from API are already divided by 100 (0.03 = 3%)
  /// All values show 2 decimal places for better user experience
  /// Examples: 1.33, 22.57K, 2.18M, 3.00%, 9.33
  String formatTooltipNumber(num value, String label) {
    if (label.toUpperCase() == 'CVR' && value is double && value < 1.0) {
      final percentage = value * 100;
      return '${percentage.toStringAsFixed(2)}%';
    }

    return formatNumberWithTwoDecimals(value);
  }

  /// Format number with 2 decimal places and appropriate units (K, M)
  /// Examples: 1.33, 22.57K, 2.18M, 1.50B
  String formatNumberWithTwoDecimals(num value) {
    if (value == 0) return '0.00';

    final absValue = value.abs();
    final isNegative = value < 0;
    final prefix = isNegative ? '-' : '';

    if (absValue >= 1000000000) {
      final billions = absValue / 1000000000;
      return '$prefix${billions.toStringAsFixed(2)}B';
    } else if (absValue >= 1000000) {
      final millions = absValue / 1000000;
      return '$prefix${millions.toStringAsFixed(2)}M';
    } else if (absValue >= 1000) {
      final thousands = absValue / 1000;
      return '$prefix${thousands.toStringAsFixed(2)}K';
    } else {
      return '$prefix${absValue.toStringAsFixed(2)}';
    }
  }

  /// Helper function to debug expected labels
  List<int> getExpectedLabels(int maxValue, double interval) {
    List<int> labels = [];
    int intervalInt = interval.round();
    if (intervalInt <= 0) intervalInt = 1; // Prevent infinite loop

    for (int i = 0; i <= maxValue; i += intervalInt) {
      labels.add(i);
      if (labels.length > 10) break; // Limit to avoid too many labels
    }
    return labels;
  }

  /// Calculate dual-axis scaling for charts with configurable intervals
  /// Returns scaling information for both left and right axes
  /// Number of intervals can be configured (default: 6)
  ChartScaling calculateDualAxisScaling(List<num> leftData, List<num> rightData, {int intervals = 6}) {
    final maxLeftValueDouble =
        leftData.isNotEmpty ? leftData.map((e) => e.toDouble()).reduce((a, b) => a > b ? a : b) : 0.0;
    final maxRightValueDouble =
        rightData.isNotEmpty ? rightData.map((e) => e.toDouble()).reduce((a, b) => a > b ? a : b) : 0.0;

    final isLeftDecimal = maxLeftValueDouble > 0 && maxLeftValueDouble < 1.0;
    final isRightDecimal = maxRightValueDouble > 0 && maxRightValueDouble < 1.0;

    double leftInterval, rightInterval, leftMaxY, rightMaxY;

    if (isLeftDecimal) {
      leftInterval = calculateDynamicIntervalForDecimals(maxLeftValueDouble);
      leftMaxY = _calculateRoundMaxY(maxLeftValueDouble, leftInterval, intervals);
    } else {
      final maxLeftValueInt = maxLeftValueDouble.toInt();
      leftInterval = calculateDynamicInterval(maxLeftValueInt);
      leftMaxY = _calculateRoundMaxY(maxLeftValueInt.toDouble(), leftInterval, intervals);
    }

    if (isRightDecimal) {
      rightInterval = calculateDynamicIntervalForDecimals(maxRightValueDouble);
      rightMaxY = _calculateRoundMaxY(maxRightValueDouble, rightInterval, intervals);
    } else {
      final maxRightValueInt = maxRightValueDouble.toInt();
      rightInterval = calculateDynamicInterval(maxRightValueInt);
      rightMaxY = _calculateRoundMaxY(maxRightValueInt.toDouble(), rightInterval, intervals);
    }

    return ChartScaling(
      leftMaxY: leftMaxY,
      rightMaxY: rightMaxY,
      leftInterval: leftInterval,
      rightInterval: rightInterval,
    );
  }

  /// Convert scaled chart value back to original value for tooltips
  /// Note: This method needs to preserve decimal precision for EPC values
  num convertScaledValueToOriginal(double scaledValue, double originalMaxY, double scaledMaxY) {
    if (originalMaxY <= 0 || scaledMaxY <= 0) return scaledValue;
    final originalValue = scaledValue * originalMaxY / scaledMaxY;

    if (originalMaxY < 1.0) {
      return originalValue;
    } else {
      return originalValue;
    }
  }

  /// Scale value from original axis to target axis for dual-axis charts
  double scaleValueForDualAxis(num originalValue, double originalMaxY, double targetMaxY) {
    if (originalMaxY <= 0 || targetMaxY <= 0) return 0.0;
    return (originalValue * targetMaxY / originalMaxY).toDouble();
  }

  /// Calculate maximum Y value that creates configurable number of intervals
  /// This ensures consistent chart appearance with configurable number of intervals
  /// Examples: intervals=4 -> 0, 2, 4, 6, 8 or intervals=6 -> 0, 200k, 400k, 600k, 800k, 1M
  double _calculateRoundMaxY(double maxValue, double interval, int intervals) {
    if (maxValue <= 0 || interval <= 0) return interval * (intervals - 1);

    return interval * (intervals - 1);
  }
}

/// A widget that provides synchronized horizontal scrolling between header and body
class _StickyHeaderTableWidget extends StatefulWidget {
  final Widget Function(ScrollController scrollController) headerBuilder;
  final Widget Function(ScrollController scrollController) bodyBuilder;

  const _StickyHeaderTableWidget({
    required this.headerBuilder,
    required this.bodyBuilder,
  });

  @override
  State<_StickyHeaderTableWidget> createState() => _StickyHeaderTableWidgetState();
}

class _StickyHeaderTableWidgetState extends State<_StickyHeaderTableWidget> {
  late ScrollController _headerScrollController;
  late ScrollController _bodyScrollController;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _headerScrollController = ScrollController();
    _bodyScrollController = ScrollController();

    _headerScrollController.addListener(_onHeaderScroll);
    _bodyScrollController.addListener(_onBodyScroll);
  }

  @override
  void dispose() {
    _headerScrollController.removeListener(_onHeaderScroll);
    _bodyScrollController.removeListener(_onBodyScroll);
    _headerScrollController.dispose();
    _bodyScrollController.dispose();
    super.dispose();
  }

  void _onHeaderScroll() {
    if (_isSyncing) return;
    _syncScrollPosition(_headerScrollController, _bodyScrollController);
  }

  void _onBodyScroll() {
    if (_isSyncing) return;
    _syncScrollPosition(_bodyScrollController, _headerScrollController);
  }

  void _syncScrollPosition(ScrollController source, ScrollController target) {
    if (!source.hasClients || !target.hasClients) return;

    final sourceOffset = source.offset;
    final targetOffset = target.offset;

    if ((sourceOffset - targetOffset).abs() > 0.5) {
      _isSyncing = true;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (target.hasClients) {
          target.jumpTo(sourceOffset);
        }
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          child: widget.headerBuilder(_headerScrollController),
        ),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: widget.bodyBuilder(_bodyScrollController),
          ),
        ),
      ],
    );
  }
}
