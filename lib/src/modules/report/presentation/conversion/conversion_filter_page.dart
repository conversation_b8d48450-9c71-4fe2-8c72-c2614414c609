import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/presentation/filter_page.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

/// Custom filter page for conversion report with Last 7 days as default after clear
class ConversionFilterPage extends StatefulWidget {
  final List<ReportPeriod> periods;
  final bool showSites;

  const ConversionFilterPage(
    this.periods, {
    required this.showSites,
    super.key,
  });

  @override
  State<ConversionFilterPage> createState() => _ConversionFilterPageState();
}

class _ConversionFilterPageState extends State<ConversionFilterPage> {
  late FilterCubit cubit;
  late TextEditingController customNameController;
  late TextEditingController invoiceNumberController;

  @override
  void initState() {
    cubit = Modular.get<FilterCubit>();
    customNameController = TextEditingController();
    invoiceNumberController = TextEditingController();
    initData();
    super.initState();
  }

  Future<void> initData() async {
    if (cubit.state.customName.isNotEmpty) {
      customNameController.text = cubit.state.customName;
    }
    if (cubit.state.invoiceId.isNotEmpty) {
      invoiceNumberController.text = cubit.state.invoiceId;
    }

    if (cubit.state.campaigns.isEmpty) {
      cubit.showLoading();
      await cubit.initSearchCondition(widget.periods, true, defaultPeriod: ReportPeriod.LAST_7_DAYS);
      cubit.hideLoading();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.white,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(Icons.close, size: 20.r),
        ),
        title: Text(
          'Filters',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        centerTitle: true,
      ),
      body: FilterPage(
        widget.periods,
        true, // showDateType
        true, // showStatus
        widget.showSites,
        true, // showCampaigns
        false, // showInvoiceNumber
        false, // showCustomName
        filterButtonName: 'Show report',
        preserveState: false,
      ),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: Row(
          spacing: 16.r,
          children: [
            Expanded(
              child: SizedBox(
                height: 40.r,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.r),
                          side: BorderSide(
                            width: 1.r,
                            color: const Color(0xFFAAAAAA),
                          )),
                      padding: EdgeInsets.symmetric(horizontal: 8.r)),
                  onPressed: _resetFilterWithCustomDefault,
                  child: Text('Clear',
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500)),
                ),
              ),
            ),
            Expanded(
                child: BlocBuilder<FilterCubit, FilterState>(
                    bloc: cubit,
                    builder: (_, state) {
                      return CommonElevatedButton(
                          'Show report',
                          (state.selectedPeriod != ReportPeriod.CUSTOM_RANGE ||
                                      (state.selectedPeriod == ReportPeriod.CUSTOM_RANGE &&
                                          state.startDate != null &&
                                          state.endDate != null)) &&
                                  state.isSearchEnabled
                              ? () {
                                  Modular.to.pop(true);
                                }
                              : null);
                    })),
          ],
        ),
      ),
    );
  }

  /// Custom reset filter method that sets Last 7 days as default
  void _resetFilterWithCustomDefault() async {
    customNameController.clear();
    invoiceNumberController.clear();

    // Use the new method that sets custom default
    await cubit.clearAndInitWithDefaultPeriod(widget.periods, true, ReportPeriod.LAST_7_DAYS);
  }

  @override
  void dispose() {
    customNameController.dispose();
    invoiceNumberController.dispose();
    super.dispose();
  }
}
