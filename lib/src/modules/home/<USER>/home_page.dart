import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_summary_horizontal_view.dart';
import 'package:koc_app/src/modules/home/<USER>/home_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/home_state.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_cubit.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/image_data.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';
import 'package:koc_app/src/shared/widgets/charts/dual_axis_line_chart.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_image_slider.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:url_launcher/url_launcher_string.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends BasePageState<HomePage, HomeCubit> with CommonMixin, ReportMixin {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.initState();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        logo: Padding(
          padding: EdgeInsets.only(left: 16.r),
          child: buildLogo(),
        ),
        showNotificationAction: true,
      ),
      body: BlocBuilder<HomeCubit, HomeState>(
        bloc: cubit,
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  Widget _buildBody(HomeState state) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 8.r),
          child: Column(
            spacing: 22.r,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonImageSlider(state.homeCarousel.map((item) => ImageData(imageUrl: item.image)).toList()),
              Padding(
                padding: EdgeInsets.only(left: 16.r, right: 16.r),
                child: Column(
                  spacing: 22.r,
                  children: [
                    _buildPerformance(state),
                    _buildCampaigns(state.promotedCampaigns, cubit.currency),
                    if (state.vouchers.isNotEmpty) _buildVoucherCode(state.vouchers),
                    _buildTools(state),
                  ],
                ),
              )

              // _buildSuperPoint(state.superPoints)
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTools(HomeState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Tools', null),
        SizedBox(height: 14.r),
        _buildToolCard(
          'Academy',
          'Learn everything you need to know about affiliate marketing.',
          'Learn now',
          const Color(0xFF1F78D1),
          state.academyLink,
          Symbols.school_sharp,
          const Color(0xFFE4F2FF),
        ),
        SizedBox(height: 8.r),
        _buildToolCard(
          'Inpages',
          'Showcase your profile, links, products easily, all in one page!',
          'Try now',
          const Color(0xFFEF8A13),
          state.inpageLink,
          Symbols.newspaper_sharp,
          const Color(0xFFFFB522).withValues(alpha: 0.3),
        ),
        SizedBox(height: 8.r),
        _buildToolCard(
          'Super point',
          'You can use it to exchange with our original gifts.',
          'Try now',
          const Color(0xFFEF6507),
          state.superPointLink,
          Symbols.star_shine_sharp,
          const Color(0xFFEF6507).withValues(alpha: 0.3),
        ),
      ],
    );
  }

  Widget _buildToolCard(String title, String description, String buttonName, Color buttonColor, String link,
      IconData icon, Color cardColor) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.all(12.r),
      child: Column(
        spacing: 12.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: context.textBodySmall(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  description,
                  style: context.textLabelLarge(color: ColorConstants.textColor),
                ),
              ),
              Icon(
                icon,
                size: 40.r,
                color: buttonColor,
                weight: 400,
              ),
            ],
          ),
          GestureDetector(
            onTap: () async {
              try {
                await launchUrlString(link, mode: LaunchMode.inAppBrowserView);
              } catch (e) {
                // do nothing.
              }
            },
            child: Row(
              spacing: 4.r,
              children: [
                Text(buttonName, style: context.textLabelLarge(fontWeight: FontWeight.w500, color: buttonColor)),
                Icon(Icons.arrow_forward, size: 20.r, color: buttonColor)
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildSuperPoint(List<SuperPoint> superPoints) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Super point', () {}),
        SizedBox(height: 14.r),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: superPoints
              .map((superPoint) => Container(
                    width: 160.r,
                    height: 155.r,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.r), color: Colors.grey[200]),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Stack(
                            children: [
                              CachedNetworkImage(
                                imageUrl: superPoint.imageUrl,
                                width: 160.r,
                                height: 96.r,
                                fit: BoxFit.cover,
                              ),
                              Positioned(
                                  bottom: 5.r,
                                  right: 5.r,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5.0.r),
                                      color: Colors.white,
                                      border: Border.all(
                                        color: Colors.grey[100]!,
                                        width: 1.0.r,
                                      ),
                                    ),
                                    child: Text(
                                      ' ${superPoint.point} ',
                                      style: Theme.of(context).textTheme.labelMedium,
                                    ),
                                  )),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(8.r),
                          child: Text(
                            superPoint.name,
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                          ),
                        )
                      ],
                    ),
                  ))
              .toList(),
        )
      ],
    );
  }

  Widget _buildVoucherCode(List<Voucher> vouchers) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Voucher code', () {
          Modular.to.pushNamed('/home/<USER>/');
        }),
        SizedBox(height: 14.r),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            spacing: 8.r,
            children: vouchers
                .map((voucher) => VoucherCard(
                      voucher,
                      width: ScreenUtil().screenWidth - 50.r,
                    ))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCampaigns(List<DefaultCampaignSummary> campaigns, String currency) {
    return MultiBlocProvider(
        providers: [
          BlocProvider.value(value: Modular.get<CampaignDetailCubit>()),
          BlocProvider.value(value: Modular.get<CreativeCubit>()),
          BlocProvider.value(value: Modular.get<VoucherCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkHistoryCubit>()),
        ],
        child: CampaignSummaryHorizontalView('Campaigns', currency, () {
          Modular.get<BottomNavigationCubit>().navigateTo(1);
        }, campaigns));
  }

  Widget _buildPerformance(HomeState state) {
    Color performanceTileColor = Colors.grey[300]!;
    final bool hasChartData = _hasChartData(state);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Performance',
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        SizedBox(height: 14.r),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: performanceTileColor, width: 1.r),
          ),
          child: Column(
            children: [
              IntrinsicHeight(
                child: Row(
                  children: [
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.earnings.current ?? 0,
                        state.lastSevenDayPerformance?.earnings.changedValue ?? 0,
                        state.lastSevenDayPerformance?.earnings.changedPercentage ?? 0,
                        cubit.currency,
                        'Earnings',
                        'Earning Occurred'),
                    VerticalDivider(
                      color: performanceTileColor,
                      width: 1.r,
                    ),
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.clicks.current ?? 0,
                        state.lastSevenDayPerformance?.clicks.changedValue ?? 0,
                        state.lastSevenDayPerformance?.clicks.changedPercentage ?? 0,
                        '',
                        'Clicks',
                        'Click'),
                  ],
                ),
              ),
              Divider(
                color: performanceTileColor,
                height: 1.r,
              ),
              IntrinsicHeight(
                child: Row(
                  children: [
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.conversions.current ?? 0,
                        state.lastSevenDayPerformance?.conversions.changedValue ?? 0,
                        state.lastSevenDayPerformance?.conversions.changedPercentage ?? 0,
                        '',
                        'Conversions',
                        'Conversion'),
                    VerticalDivider(
                      color: performanceTileColor,
                      width: 1.r,
                    ),
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.earningsPerClick.current ?? 0,
                        state.lastSevenDayPerformance?.earningsPerClick.changedValue ?? 0,
                        state.lastSevenDayPerformance?.earningsPerClick.changedPercentage ?? 0,
                        cubit.currency,
                        'EPC',
                        'EPC Occurred'),
                  ],
                ),
              ),
              if (hasChartData) ...[
                Divider(
                  color: performanceTileColor,
                  height: 1.r,
                ),
                if (state.showChart) _buildChart(state),
                if (state.showChart)
                  Divider(
                    color: performanceTileColor,
                    height: 1.r,
                  ),
                IconButton(
                  onPressed: () {
                    cubit.toggleChart();
                  },
                  icon: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${state.showChart ? 'Hide' : 'View'} chart',
                        style: context.textLabelLarge(
                          color: const Color(0xFFEF6507),
                        ),
                      ),
                      Icon(
                          color: const Color(0xFFEF6507),
                          state.showChart ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                          size: 24.r),
                    ],
                  ),
                ),
              ],
            ],
          ),
        )
      ],
    );
  }

  Widget _buildChart(HomeState state) {
    final List<String> days = List.generate(7, (index) {
      DateTime date = DateTime.now().subtract(Duration(days: 6 - index));
      return DateFormat('MMM d').format(date);
    });

    final List<num> clicks = state.clicks.length == 7 ? state.clicks : List.filled(7, 0);
    final List<num> conversions = state.conversions.length == 7 ? state.conversions : List.filled(7, 0);

    final chartData = DualAxisChartData(
      labels: days,
      leftAxisData: clicks,
      rightAxisData: conversions,
      leftAxisLabel: 'Clicks',
      rightAxisLabel: 'Conversions',
    );

    const chartConfig = DualAxisChartConfig(
      showLegend: true,
      aspectRatio: 1.5,
      normalHeight: 200.0,
      emptyHeight: 122.0,
      showContainer: true,
      containerPadding:  EdgeInsets.symmetric(horizontal: 4.0, vertical: 12.0),
      borderColor: Color.fromARGB(0, 231, 231, 231),
    );

    return DualAxisLineChart(
      data: chartData,
      config: chartConfig,
    );
  }

  Widget _buildPerformanceTile(
      num current, num changeValue, num changedPercentage, String currency, String label, String description) {
    bool isIncreased = changeValue > 0;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(8.0.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              current.toPrice(currency),
              style: context.textBodySmall(fontSize: 16.r, fontWeight: FontWeight.bold),
            ),
            if (changeValue != 0)
              Row(
                children: [
                  Icon(isIncreased ? Icons.arrow_upward : Icons.arrow_downward,
                      color: isIncreased ? Colors.green : Colors.red, size: 12.r),
                  Text(
                    '${changeValue.toCommaSeparated()} (${changedPercentage.toPercentageChanged()})',
                    style: context.textLabelMedium(
                      fontSize: 12.r,
                      color: const Color(0xFF767676),
                    ),
                  ),
                ],
              ),
            Row(
              children: [
                Text(
                  label,
                  style: context.textLabelLarge(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
                ),
                GestureDetector(
                  onTap: () {
                    showDescription(context, label, description);
                  },
                  child: Icon(Icons.help_outline, size: 16.r, color: const Color(0xFF767676)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool _hasChartData(HomeState state) {
    return state.clicks.any((c) => c > 0) || state.conversions.any((c) => c > 0);
  }
}
